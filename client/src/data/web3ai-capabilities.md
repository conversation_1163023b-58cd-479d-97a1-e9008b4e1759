# What Web3AI Can Do

**Web3AI** is a natural language model with improved blockchain reasoning, autonomous transaction capabilities, and real-time access to the blockchain.

Here are some example actions you can perform with Web3AI:

## 🌉 Bridge & Swap

Bridge and swap native currencies across different networks

- Swap 1 USDC to 1 USDT on the Ethereum Mainnet
- Bridge 0.5 ETH from Ethereum Mainnet to Polygon

## 📤 Transfer

Send native and ERC-20 currencies to any address

- Send 0.1 ETH to `vitalik.eth`
- Transfer 1 USDC to `saminacodes.eth` on Base

## 🚀 Deploy

Deploy published contracts with custom configurations

- Deploy a Token ERC20 Contract with name "Hello World" and description "My Hello Contract" on Ethereum
- Deploy a Split contract with two recipients
- Deploy an ERC1155 Contract named 'Hello World' with description 'Hello badges on Ethereum'

## 🔍 Understand

Retrieve detailed information about smart contracts

- What ERC standards are implemented by contract address `******************************************` on Ethereum?
- What functions can I use to mint more of my contract's NFTs?
- What is the total supply of NFTs on `******************************************`?

## 💰 Interact

Query wallet balances, addresses, and token holdings

- How much ETH is in my wallet?
- What is the wallet address of `vitalik.eth`?
- Does my wallet hold USDC on Base?

## 🔎 Explore

Access real-time blockchain-specific data

- What is the last block on zkSync?
- What is the current gas price on Avalanche C-Chain?
- Can you show me transaction details for `0xdfc450bb39e44bd37c22e0bfd0e5212edbea571e4e534d87b5cbbf06f10b9e04` on Optimism?

## 📊 Research

Obtain details about tokens, their addresses, and current prices

- What is the address of USDC on Ethereum?
- Is there a UNI token on Arbitrum?
- What is the current price of ARB?

## 🛠️ Build

Implement features using Web3 SDKs and tools

- How can I add a connect wallet button to my web app? I want to support users connecting with both email/social wallets and MetaMask and use smart wallets
- Can you show me how to claim an NFT from an ERC721 using TypeScript?
- I have an ERC1155 contract from thirdweb. Can you show me how to generate and mint with a signature?

---

_Ready to get started? Just ask me anything about blockchain development, DeFi, NFTs, or smart contracts!_
