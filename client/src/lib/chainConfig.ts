import { getChainMetadata, getRpcUrlForChain } from "thirdweb/chains";
import type { Chain } from "thirdweb/chains";
import {
  client as thirdwebClient,
  ethereum,
  arbitrum,
  optimism,
  base,
  polygon,
  bsc,
  avalanche,
  fantom,
  sepolia,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  polygonAmoy,
  bscTestnet,
} from "./thirdweb"; // Use existing client and chains

// Re-export client for convenience
export const client = thirdwebClient;

// Define supported chains using thirdweb's predefined chains
export const supportedChains: Chain[] = [
  // Ethereum and Layer 2s
  ethereum, // Ethereum Mainnet
  arbitrum, // Arbitrum One
  optimism, // Optimism
  base, // Base

  // Alternative Layer 1s
  polygon, // Polygon Mainnet
  bsc, // BNB Smart Chain
  avalanche, // Avalanche C-Chain
  fantom, // Fantom Opera

  // Testnets
  sepolia, // Sepolia Testnet
  arbitrumSepolia, // Arbitrum Sepolia
  optimismSepolia, // Optimism Sepolia
  baseSepolia, // Base Sepolia
  polygonAmoy, // Polygon Amoy Testnet
  bscTestnet, // BSC Testnet
];

// Legacy chain colors for UI consistency (can be removed later)
export const chainColors: Record<number, string> = {
  // Ethereum and Layer 2s
  1: "#627EEA", // Ethereum
  42161: "#28A0F0", // Arbitrum One
  10: "#FF0420", // Optimism
  8453: "#0052FF", // Base

  // Alternative Layer 1s
  137: "#8247E5", // Polygon
  56: "#F3BA2F", // BNB Smart Chain
  43114: "#E84142", // Avalanche C-Chain
  250: "#1969FF", // Fantom Opera

  // Testnets
  11155111: "#627EEA", // Sepolia
  421614: "#28A0F0", // Arbitrum Sepolia
  11155420: "#FF0420", // Optimism Sepolia
  84532: "#0052FF", // Base Sepolia
  80002: "#8247E5", // Polygon Amoy
  97: "#F3BA2F", // BSC Testnet
};

// Get chain by chain ID
export const getChainById = (chainId: number): Chain | undefined => {
  return supportedChains.find((chain) => chain.id === chainId);
};

// Get chain by name (async since we need to fetch metadata)
export const getChainByName = async (
  name: string
): Promise<Chain | undefined> => {
  for (const chain of supportedChains) {
    try {
      const metadata = await getChainMetadata(chain);
      if (metadata.name.toLowerCase() === name.toLowerCase()) {
        return chain;
      }
    } catch (error) {
      console.warn(`Failed to get metadata for chain ${chain.id}:`, error);
    }
  }
  return undefined;
};

// Block Explorer Configuration using thirdweb metadata
export interface BlockExplorer {
  name: string;
  url: string;
  apiUrl?: string;
}

// Get block explorers using thirdweb's chain metadata
export const getBlockExplorers = async (
  chainId: number
): Promise<BlockExplorer[]> => {
  const chain = getChainById(chainId);
  if (!chain) {
    return [{ name: "Etherscan", url: "https://etherscan.io" }];
  }

  try {
    const metadata = await getChainMetadata(chain);
    if (metadata.explorers && metadata.explorers.length > 0) {
      return metadata.explorers.map((explorer) => ({
        name: explorer.name,
        url: explorer.url,
        apiUrl: (explorer as any).apiUrl, // Type assertion since apiUrl might not be in the type
      }));
    }
  } catch (error) {
    console.warn(`Failed to get explorers for chain ${chainId}:`, error);
  }

  // Fallback to default
  return [{ name: "Etherscan", url: "https://etherscan.io" }];
};

// Generate explorer URLs for different types of data
export const getExplorerUrl = async (
  chainId: number,
  type: "address" | "tx" | "block" | "token",
  value: string
): Promise<string> => {
  const explorers = await getBlockExplorers(chainId);
  const baseUrl = explorers[0]?.url || "https://etherscan.io";

  switch (type) {
    case "address":
      return `${baseUrl}/address/${value}`;
    case "tx":
      return `${baseUrl}/tx/${value}`;
    case "block":
      return `${baseUrl}/block/${value}`;
    case "token":
      return `${baseUrl}/token/${value}`;
    default:
      return baseUrl;
  }
};

// Helper function to get chain metadata with caching
const chainMetadataCache = new Map<number, any>();

export const getCachedChainMetadata = async (chain: Chain) => {
  if (chainMetadataCache.has(chain.id)) {
    return chainMetadataCache.get(chain.id);
  }

  try {
    const metadata = await getChainMetadata(chain);
    chainMetadataCache.set(chain.id, metadata);
    return metadata;
  } catch (error) {
    console.warn(`Failed to get metadata for chain ${chain.id}:`, error);
    return null;
  }
};

// Helper function to get RPC URL for a chain
export const getChainRpcUrl = (chain: Chain): string => {
  try {
    return getRpcUrlForChain({ chain, client });
  } catch (error) {
    console.warn(`Failed to get RPC URL for chain ${chain.id}:`, error);
    // Fallback RPC URLs
    const fallbackRpcs: Record<number, string> = {
      // Ethereum and Layer 2s
      1: "https://ethereum.rpc.thirdweb.com",
      42161: "https://arbitrum.rpc.thirdweb.com",
      10: "https://optimism.rpc.thirdweb.com",
      8453: "https://base.rpc.thirdweb.com",

      // Alternative Layer 1s
      137: "https://polygon.rpc.thirdweb.com",
      56: "https://binance.rpc.thirdweb.com",
      43114: "https://avalanche.rpc.thirdweb.com",
      250: "https://fantom.rpc.thirdweb.com",

      // Testnets
      11155111: "https://sepolia.rpc.thirdweb.com",
      421614: "https://arbitrum-sepolia.rpc.thirdweb.com",
      11155420: "https://optimism-sepolia.rpc.thirdweb.com",
      84532: "https://base-sepolia.rpc.thirdweb.com",
      80002: "https://amoy.rpc.thirdweb.com",
      97: "https://bsc-testnet.rpc.thirdweb.com",
    };
    return fallbackRpcs[chain.id] || "https://ethereum.rpc.thirdweb.com";
  }
};
