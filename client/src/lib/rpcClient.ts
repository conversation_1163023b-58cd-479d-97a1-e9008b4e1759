// Dynamic RPC Client for multiple blockchain networks
// Handles CORS issues by proxying through the backend

export interface RPCRequest {
  jsonrpc: string;
  method: string;
  params: any[];
  id: number;
}

export interface RPCResponse {
  jsonrpc: string;
  result?: any;
  error?: {
    code: number;
    message: string;
  };
  id: number;
}

/**
 * Make RPC calls to any supported blockchain network
 * @param chainId - The chain ID (e.g., "1" for Ethereum, "137" for Polygon)
 * @param method - RPC method (e.g., "eth_blockNumber", "eth_getBalance")
 * @param params - Parameters for the RPC method
 * @returns Promise with the RPC result
 */
export const makeRPCCall = async (
  chainId: string | number,
  method: string,
  params: any[] = []
): Promise<any> => {
  try {
    const response = await fetch(`/api/rpc/${chainId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        jsonrpc: "2.0",
        method,
        params,
        id: Date.now(),
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }

    const data: RPCResponse = await response.json();
    
    if (data.error) {
      throw new Error(`RPC Error: ${data.error.message} (Code: ${data.error.code})`);
    }

    return data.result;
  } catch (error) {
    console.error(`RPC call failed for chain ${chainId}:`, error);
    throw error;
  }
};

/**
 * Get the current block number for a specific chain
 */
export const getBlockNumber = async (chainId: string | number): Promise<string> => {
  return await makeRPCCall(chainId, "eth_blockNumber");
};

/**
 * Get balance of an address on a specific chain
 */
export const getBalance = async (
  chainId: string | number,
  address: string,
  blockTag: string = "latest"
): Promise<string> => {
  return await makeRPCCall(chainId, "eth_getBalance", [address, blockTag]);
};

/**
 * Get gas price for a specific chain
 */
export const getGasPrice = async (chainId: string | number): Promise<string> => {
  return await makeRPCCall(chainId, "eth_gasPrice");
};

/**
 * Get transaction count (nonce) for an address
 */
export const getTransactionCount = async (
  chainId: string | number,
  address: string,
  blockTag: string = "latest"
): Promise<string> => {
  return await makeRPCCall(chainId, "eth_getTransactionCount", [address, blockTag]);
};

/**
 * Get transaction by hash
 */
export const getTransaction = async (
  chainId: string | number,
  txHash: string
): Promise<any> => {
  return await makeRPCCall(chainId, "eth_getTransactionByHash", [txHash]);
};

/**
 * Get transaction receipt
 */
export const getTransactionReceipt = async (
  chainId: string | number,
  txHash: string
): Promise<any> => {
  return await makeRPCCall(chainId, "eth_getTransactionReceipt", [txHash]);
};

/**
 * Call a contract method (read-only)
 */
export const call = async (
  chainId: string | number,
  callObject: {
    to: string;
    data?: string;
    from?: string;
    gas?: string;
    gasPrice?: string;
    value?: string;
  },
  blockTag: string = "latest"
): Promise<string> => {
  return await makeRPCCall(chainId, "eth_call", [callObject, blockTag]);
};

/**
 * Estimate gas for a transaction
 */
export const estimateGas = async (
  chainId: string | number,
  transactionObject: {
    to?: string;
    from?: string;
    gas?: string;
    gasPrice?: string;
    value?: string;
    data?: string;
  }
): Promise<string> => {
  return await makeRPCCall(chainId, "eth_estimateGas", [transactionObject]);
};

/**
 * Get supported chain information
 */
export const getSupportedChains = () => {
  return {
    // Mainnets
    "1": { name: "Ethereum", symbol: "ETH", testnet: false },
    "137": { name: "Polygon", symbol: "MATIC", testnet: false },
    "56": { name: "BSC", symbol: "BNB", testnet: false },
    "42161": { name: "Arbitrum", symbol: "ETH", testnet: false },
    "10": { name: "Optimism", symbol: "ETH", testnet: false },
    "43114": { name: "Avalanche", symbol: "AVAX", testnet: false },
    "8453": { name: "Base", symbol: "ETH", testnet: false },
    "250": { name: "Fantom", symbol: "FTM", testnet: false },
    "25": { name: "Cronos", symbol: "CRO", testnet: false },
    "1284": { name: "Moonbeam", symbol: "GLMR", testnet: false },
    "42220": { name: "Celo", symbol: "CELO", testnet: false },
    
    // Testnets
    "11155111": { name: "Sepolia", symbol: "ETH", testnet: true },
    "80002": { name: "Amoy", symbol: "MATIC", testnet: true },
    "97": { name: "BSC Testnet", symbol: "BNB", testnet: true },
    "421614": { name: "Arbitrum Sepolia", symbol: "ETH", testnet: true },
    "11155420": { name: "Optimism Sepolia", symbol: "ETH", testnet: true },
    "84532": { name: "Base Sepolia", symbol: "ETH", testnet: true },
    "43113": { name: "Avalanche Fuji", symbol: "AVAX", testnet: true },
    "4002": { name: "Fantom Testnet", symbol: "FTM", testnet: true },
    "338": { name: "Cronos Testnet", symbol: "CRO", testnet: true },
    "1287": { name: "Moonbase Alpha", symbol: "DEV", testnet: true },
    "44787": { name: "Celo Alfajores", symbol: "CELO", testnet: true },
  };
};

/**
 * Convert hex to decimal
 */
export const hexToDecimal = (hex: string): number => {
  return parseInt(hex, 16);
};

/**
 * Convert decimal to hex
 */
export const decimalToHex = (decimal: number): string => {
  return "0x" + decimal.toString(16);
};

/**
 * Convert wei to ether (18 decimals)
 */
export const weiToEther = (wei: string): string => {
  const weiNum = BigInt(wei);
  const etherNum = Number(weiNum) / Math.pow(10, 18);
  return etherNum.toString();
};

/**
 * Convert ether to wei (18 decimals)
 */
export const etherToWei = (ether: string): string => {
  const etherNum = parseFloat(ether);
  const weiNum = BigInt(Math.floor(etherNum * Math.pow(10, 18)));
  return "0x" + weiNum.toString(16);
};
