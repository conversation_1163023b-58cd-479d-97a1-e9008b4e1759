import { apiRequest } from "@/lib/queryClient";

// Chat interface types based on existing app schema
export interface NebulaChat {
  id: number;
  title: string;
  createdAt: string;
  updatedAt: string;
  metadata?: {
    chainId?: string;
    topic?: string;
  };
}

export interface NebulaMessage {
  id: number;
  chatId: number;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  metadata?: {
    chainId?: string;
    source?: string;
    executionTime?: number;
    blockchainData?: any;
    sender?: string;
    isStreaming?: boolean;
  };
}

/**
 * Helper to format API chats into NebulaChat format
 */
export function formatApiChats(apiChats: any[]): NebulaChat[] {
  return apiChats.map((chat) => ({
    id: chat.id,
    title: chat.title || `Chat ${chat.id}`,
    createdAt: chat.createdAt || new Date().toISOString(),
    updatedAt: chat.updatedAt || new Date().toISOString(),
    metadata: chat.metadata || {},
  }));
}

/**
 * Helper to format API messages into NebulaMessage format
 */
export function formatApiMessages(apiMessages: any[]): NebulaMessage[] {
  return apiMessages.map((message) => ({
    id: message.id,
    chatId: message.chatId,
    role: message.role,
    content: message.content,
    timestamp: message.timestamp || new Date().toISOString(),
    metadata: message.metadata || {},
  }));
}

/**
 * Get all chats for the current user
 */
export async function getNebulaChats(
  walletAddress?: string
): Promise<NebulaChat[]> {
  try {
    const url = walletAddress
      ? `/api/chats?walletAddress=${encodeURIComponent(walletAddress)}`
      : "/api/chats";
    const response = await apiRequest("GET", url);
    const data = await response.json();
    return formatApiChats(data);
  } catch (error) {
    console.error("Failed to get chats:", error);
    throw error;
  }
}

/**
 * Create a new chat
 */
export async function createNebulaChat(
  title: string,
  walletAddress: string,
  metadata?: any
): Promise<NebulaChat> {
  try {
    const response = await apiRequest("POST", "/api/chats", {
      title,
      walletAddress,
      metadata,
    });
    const data = await response.json();
    return {
      id: data.id,
      title: data.title || title,
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: data.updatedAt || new Date().toISOString(),
      metadata: data.metadata || metadata || {},
    };
  } catch (error) {
    console.error("Failed to create chat:", error);
    throw error;
  }
}

/**
 * Get messages for a specific chat
 */
export async function getNebulaChatMessages(
  chatId: number,
  walletAddress?: string
): Promise<NebulaMessage[]> {
  try {
    const url = walletAddress
      ? `/api/chats/${chatId}/messages?walletAddress=${encodeURIComponent(
          walletAddress
        )}`
      : `/api/chats/${chatId}/messages`;
    const response = await apiRequest("GET", url);
    const data = await response.json();
    return formatApiMessages(data);
  } catch (error) {
    console.error("Failed to get chat messages:", error);
    throw error;
  }
}

/**
 * Send a message to a chat with wallet and chain context
 */
export async function sendNebulaMessage(
  chatId: number,
  content: string,
  walletAddress: string,
  metadata?: any,
  sessionId?: string,
  chainId?: number,
  account?: any
): Promise<NebulaMessage> {
  try {
    const response = await apiRequest("POST", `/api/chats/${chatId}/messages`, {
      content,
      walletAddress,
      metadata: {
        ...metadata,
        chainId,
        walletAddress,
      },
      sessionId,
      account,
    });
    const data = await response.json();

    // Handle both user and assistant messages from the response
    if (data.userMessage && data.assistantMessage) {
      // Return the user message, the assistant message will be fetched separately
      return {
        id: data.userMessage.id,
        chatId: data.userMessage.chatId,
        role: data.userMessage.role,
        content: data.userMessage.content,
        timestamp: data.userMessage.timestamp || new Date().toISOString(),
        metadata: data.userMessage.metadata || metadata || {},
      };
    }

    return {
      id: data.id,
      chatId: data.chatId,
      role: data.role,
      content: data.content,
      timestamp: data.timestamp || new Date().toISOString(),
      metadata: data.metadata || metadata || {},
    };
  } catch (error) {
    console.error("Failed to send message:", error);
    throw error;
  }
}
