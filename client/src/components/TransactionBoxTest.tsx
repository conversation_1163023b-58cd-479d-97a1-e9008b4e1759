import ChatMessage from "@/components/ChatMessage";

const TransactionBoxTest = () => {
  // Simulate the ERC20 deployment response message
  const testMessage = {
    id: 1,
    role: "assistant",
    content: `Your ERC20 token deployment is now ready for Ethereum Mainnet:

Name: Hello World
Symbol: HELLO
Description: My Hello Contract
Chain: Ethereum Mainnet (Chain ID: 1)
Predicted Contract Address: ******************************************
Platform Fee: 1.5% (recipient: ******************************************)
Admin & Sale Recipient: ****************************************** (default, can be set post-deployment)
Status: Deployment transaction is prepared (unsigned)

What you can do after deployment:

Mint tokens (mintTo)
Transfer tokens (transfer)
Approve spending (approve)
Grant/revoke roles (grantRole)
Burn tokens (burn)
Set contract/sale metadata and platform fee recipients

You can now sign and broadcast your deployment transaction to publish the contract on Ethereum.

Would you like to proceed with deployment, or review/customize advanced parameters before going live?`,
    timestamp: new Date().toISOString(),
    metadata: {
      chainId: "1",
      source: "Web3AI",
      executionTime: 1500,
      // No blockchainData to test text parsing
    },
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-lg font-semibold mb-4">TransactionBox Test - ERC20 Deployment</h2>
      <div className="border border-border rounded-lg">
        <ChatMessage message={testMessage} />
      </div>
      
      <div className="mt-6 p-4 bg-muted/30 rounded-md">
        <h3 className="text-sm font-medium mb-2">Test Details:</h3>
        <p className="text-xs text-muted-foreground">
          This test simulates an ERC20 deployment response without structured blockchain data.
          The TransactionBox should be automatically generated from the text content using pattern matching.
        </p>
      </div>
    </div>
  );
};

export default TransactionBoxTest;
