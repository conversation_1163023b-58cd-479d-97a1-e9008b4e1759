import {
  ArrowRightIcon,
  HelpCircleIcon,
  CodeIcon,
  DollarSignIcon,
  SearchIcon,
  ShieldCheckIcon,
} from "lucide-react";
import { useState } from "react";

interface SuggestionChipsProps {
  onChipClick: (suggestion: string) => void;
}

interface SuggestionItem {
  text: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
}

const suggestions: SuggestionItem[] = [
  {
    text: "What Web3AI can do?",
    icon: HelpCircleIcon,
    gradient: "from-purple-500/20 to-pink-500/20",
  },
  {
    text: "Create an ERC-20 token",
    icon: CodeIcon,
    gradient: "from-blue-500/20 to-cyan-500/20",
  },
  {
    text: "Buy stablecoins",
    icon: DollarSignIcon,
    gradient: "from-green-500/20 to-emerald-500/20",
  },
  {
    text: "Audit smart contracts",
    icon: ShieldCheckIcon,
    gradient: "from-orange-500/20 to-red-500/20",
  },
  {
    text: "Check gas prices",
    icon: SearchIcon,
    gradient: "from-indigo-500/20 to-purple-500/20",
  },
];

const SuggestionChips = ({ onChipClick }: SuggestionChipsProps) => {
  const [clickedIndex, setClickedIndex] = useState<number | null>(null);

  const handleClick = (suggestion: string, index: number) => {
    setClickedIndex(index);
    setTimeout(() => setClickedIndex(null), 200);
    onChipClick(suggestion);
  };

  return (
    <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-4 px-2">
      {suggestions.map((suggestion, index) => {
        const IconComponent = suggestion.icon;
        return (
          <button
            key={index}
            className={`
              group relative overflow-hidden
              nebula-suggestion-chip-enhanced
              text-xs sm:text-sm min-h-[36px] px-3 sm:px-4 py-2 sm:py-2.5
              transition-all duration-300 ease-out
              hover:scale-105 hover:shadow-lg hover:shadow-primary/25
              active:scale-95
              ${clickedIndex === index ? "animate-bounce" : ""}
            `}
            style={{
              animationDelay: `${index * 100}ms`,
            }}
            onClick={() => handleClick(suggestion.text, index)}
          >
            {/* Gradient background overlay */}
            <div
              className={`
              absolute inset-0 bg-gradient-to-r ${suggestion.gradient}
              opacity-0 group-hover:opacity-100 transition-opacity duration-300
            `}
            />

            {/* Ripple effect */}
            <div
              className={`
              absolute inset-0 bg-primary/10 rounded-full scale-0
              ${clickedIndex === index ? "animate-ping" : ""}
            `}
            />

            {/* Content */}
            <div className="relative flex items-center gap-2">
              <IconComponent className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-primary/80 group-hover:text-primary transition-colors duration-200" />
              <span className="truncate font-medium">{suggestion.text}</span>
              <ArrowRightIcon className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-primary/60 group-hover:text-primary group-hover:translate-x-0.5 transition-all duration-200 flex-shrink-0" />
            </div>
          </button>
        );
      })}
    </div>
  );
};

export default SuggestionChips;
