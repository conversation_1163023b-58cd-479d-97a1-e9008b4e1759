import { useState } from "react";
import TransactionBox from "@/components/TransactionBox";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/hooks/use-theme";
import { Sun, Moon } from "lucide-react";

const TransactionBoxDemo = () => {
  const [transactionHash, setTransactionHash] = useState<string | null>(null);
  const { theme, toggleTheme } = useTheme();

  // Example transaction data
  const exampleTransaction = {
    from: "0x4ECb...b8c5",
    to: "0x2554...07a1",
    value: "0", // 0 ETH
    chainId: 1, // Ethereum Mainnet
    data: "0x",
    gasLimit: "21000",
    gasPrice: "20000000000", // 20 gwei
  };

  // Mock execute function
  const handleExecuteTransaction = async (txData: any) => {
    console.log("Executing transaction:", txData);

    // Simulate transaction execution
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Mock transaction hash
    const mockHash =
      "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
    setTransactionHash(mockHash);

    return mockHash;
  };

  const resetDemo = () => {
    setTransactionHash(null);
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="p-6 max-w-md mx-auto">
        {/* Header with theme toggle */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">TransactionBox Demo</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="h-9 w-9 p-0"
            title={
              theme === "dark" ? "Switch to light mode" : "Switch to dark mode"
            }
          >
            {theme === "dark" ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>
        </div>

        <TransactionBox
          title="Transaction"
          transactionData={exampleTransaction}
          onExecute={handleExecuteTransaction}
          transactionHash={transactionHash}
        />

        {transactionHash && (
          <Button onClick={resetDemo} className="mt-4 w-full" variant="outline">
            Reset Demo
          </Button>
        )}

        <div className="mt-6 p-4 bg-muted/30 rounded-md">
          <h3 className="text-sm font-medium mb-2">Usage Example:</h3>
          <pre className="text-xs text-muted-foreground overflow-x-auto">
            {`<TransactionBox
  title="Transaction"
  transactionData={{
    from: "0x4ECb...b8c5",
    to: "0x2554...07a1",
    value: "0",
    chainId: 1,
  }}
  onExecute={handleExecute}
  transactionHash={txHash}
/>`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TransactionBoxDemo;
