# TransactionBox Component

A reusable React component for displaying blockchain transaction information in a styled card format, matching the Web3AI platform design.

## Features

- **Transaction Details Display**: Shows From/To addresses, value, and network information
- **Colored Address Indicators**: Yellow dot for "From" address, blue dot for "To" address  
- **Network Support**: Supports multiple blockchain networks (Ethereum, Polygon, BSC, etc.)
- **Execute Functionality**: Built-in transaction execution with loading states
- **Explorer Integration**: Automatic explorer link generation after transaction execution
- **Responsive Design**: Works on mobile and desktop
- **Nebula Styling**: Uses the platform's custom CSS classes for consistent theming

## Props

```typescript
interface TransactionBoxProps {
  title?: string;                    // Card title (default: "Transaction")
  transactionData: TransactionData;  // Transaction details
  onExecute?: (txData: TransactionData) => Promise<string | void>; // Execute handler
  isExecuted?: boolean;              // Whether transaction is already executed
  transactionHash?: string;          // Hash of executed transaction
  className?: string;                // Additional CSS classes
}

interface TransactionData {
  from?: string;      // Sender address
  to?: string;        // Recipient address  
  value?: string | bigint; // Transaction value in wei
  chainId?: number;   // Blockchain network ID
  data?: string;      // Transaction data
  gasLimit?: string;  // Gas limit
  gasPrice?: string;  // Gas price
  nonce?: number;     // Transaction nonce
}
```

## Usage Examples

### Basic Usage
```tsx
import TransactionBox from "@/components/TransactionBox";

<TransactionBox
  transactionData={{
    from: "0x4ECb...b8c5",
    to: "0x2554...07a1", 
    value: "1000000000000000000", // 1 ETH in wei
    chainId: 1
  }}
/>
```

### With Execute Function
```tsx
<TransactionBox
  title="Send ETH"
  transactionData={txData}
  onExecute={async (txData) => {
    const hash = await sendTransaction(txData);
    return hash;
  }}
/>
```

### With Transaction Hash (Executed State)
```tsx
<TransactionBox
  transactionData={txData}
  transactionHash="0x1234...abcd"
/>
```

## Supported Networks

- Ethereum Mainnet (1)
- Polygon Mainnet (137) 
- BSC Mainnet (56)
- Sepolia Testnet (11155111)
- Amoy Testnet (80002)
- BSC Testnet (97)

## Styling

The component uses these CSS classes from the platform:
- `nebula-transaction-card` - Main card styling
- `nebula-transaction-row` - Row layout
- `nebula-transaction-label` - Label styling
- `nebula-transaction-value` - Value styling
- `nebula-action-button` - Button styling

## Integration with Chat Messages

The component is already integrated into the ChatMessage component and will automatically render when transaction data is present in the message metadata.
