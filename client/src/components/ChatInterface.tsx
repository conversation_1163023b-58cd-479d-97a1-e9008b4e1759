import { useEffect, useRef, useState } from "react";
import ChatMessage from "@/components/ChatMessage";
import MessageInput from "@/components/MessageInput";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";

interface ChatInterfaceProps {
  messages: any[];
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
}

const ChatInterface = ({
  messages,
  onSendMessage,
  isLoading = false,
}: ChatInterfaceProps) => {
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const isAutoScrolling = useRef(false);
  const wasAtBottomRef = useRef(true); // Track if user was at bottom before content change

  // Helper function to check if user is at bottom
  const isAtBottom = () => {
    if (!chatContainerRef.current) return true;
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    return scrollHeight - scrollTop - clientHeight < 50; // 50px threshold
  };

  // Helper function to scroll to bottom smoothly
  const scrollToBottom = (smooth: boolean = false) => {
    if (chatContainerRef.current) {
      isAutoScrolling.current = true;
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
      // Reset auto-scrolling flag after scroll completes
      setTimeout(
        () => {
          isAutoScrolling.current = false;
          wasAtBottomRef.current = true; // User is now at bottom
          setUserHasScrolled(false);
          setShowScrollToBottom(false);
        },
        smooth ? 300 : 50
      );
    }
  };

  // Handle scroll events to detect user scrolling
  const handleScroll = () => {
    if (isAutoScrolling.current) return;

    const atBottom = isAtBottom();
    wasAtBottomRef.current = atBottom; // Update the ref to track current position
    setUserHasScrolled(!atBottom);

    // Show scroll to bottom button if user scrolled up and there's activity (loading or recent messages)
    setShowScrollToBottom(!atBottom && (isLoading || messages.length > 0));
  };

  // Scroll to bottom when messages change (only if user was at bottom before the change)
  useEffect(() => {
    // Only auto-scroll if user was at the bottom before this content change
    // This prevents interrupting users who have scrolled up to read previous messages
    if (messages && messages.length > 0 && wasAtBottomRef.current) {
      scrollToBottom();
    } else if (messages && messages.length > 0) {
      // If user has scrolled up, show the scroll to bottom button
      const atBottom = isAtBottom();
      setShowScrollToBottom(!atBottom);
    }
  }, [messages]);

  // Update scroll to bottom button visibility when loading state changes
  useEffect(() => {
    if (chatContainerRef.current) {
      const atBottom = isAtBottom();
      setShowScrollToBottom(!atBottom && (isLoading || messages.length > 0));
    }
  }, [isLoading, userHasScrolled, messages.length]);

  return (
    <div className="flex flex-col h-full relative">
      {/* Chat Messages */}
      <div
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto chat-container px-2 sm:px-0"
        onScroll={handleScroll}
      >
        {messages.length > 0 && (
          <div className="py-4 sm:py-6 relative">
            {messages.map((message: any) => (
              <div key={message.id} className="relative">
                <ChatMessage message={message} />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Fixed scroll to bottom button - shows when user has scrolled up during loading/streaming */}
      {showScrollToBottom && (
        <Button
          onClick={() => scrollToBottom(true)}
          className="fixed bottom-20 right-6 z-50 h-12 w-12 rounded-full p-0 shadow-lg bg-primary hover:bg-primary/90 border border-primary-foreground/20 transition-all duration-200 hover:scale-105"
          title="Scroll to bottom"
        >
          <ChevronDown className="h-5 w-5" />
        </Button>
      )}

      {/* Input Area */}
      <div className="p-3 sm:p-4">
        <MessageInput
          onSendMessage={onSendMessage}
          isLoading={isLoading}
          compact={false}
        />
      </div>
    </div>
  );
};

export default ChatInterface;
