import { useState, useCallback } from "react";
import {
  sendNebulaMessage,
  getOrCreateSessionId,
  NebulaContextFilter,
} from "@/lib/nebulaApi";
import { toast } from "@/hooks/use-toast";
import { useActiveAccount, useActiveWalletChain } from "thirdweb/react";

export function useNebulaApi() {
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(() => getOrCreateSessionId());
  const account = useActiveAccount();
  const activeChain = useActiveWalletChain();

  const sendMessage = useCallback(
    async (message: string) => {
      if (!message.trim()) return null;

      setIsLoading(true);

      // Create context filter with current wallet and chain
      const contextFilter: NebulaContextFilter = {};

      if (account?.address) {
        contextFilter.walletAddresses = [account.address];
      }

      if (activeChain?.id) {
        contextFilter.chainIds = [activeChain.id];
      }

      try {
        const response = await sendNebulaMessage(
          message,
          contextFilter,
          sessionId,
          false
        );

        toast({
          title: "Message sent",
          description: "Received response from Nebula AI",
        });

        return response;
      } catch (error: any) {
        console.error("Error sending message to Nebula:", error);

        // Handle specific error cases
        if (error.message.includes("secret key")) {
          toast({
            title: "API Key Required",
            description:
              "Please configure your ThirdWeb secret key to use Nebula Chat",
            variant: "destructive",
          });
        } else {
          toast({
            title: "Error sending message",
            description: error.message || "Failed to send message to Nebula",
            variant: "destructive",
          });
        }

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [sessionId, account?.address, activeChain?.id]
  );

  const sendStreamingMessage = useCallback(
    async (message: string, onChunk?: (chunk: string) => void) => {
      if (!message.trim()) return null;

      setIsLoading(true);

      // Create context filter with current wallet and chain
      const contextFilter: NebulaContextFilter = {};

      if (account?.address) {
        contextFilter.walletAddresses = [account.address];
      }

      if (activeChain?.id) {
        contextFilter.chainIds = [activeChain.id];
      }

      try {
        // Import the streaming function
        const { sendNebulaMessageStream } = await import("@/lib/nebulaApi");

        let finalResponse: any = null;

        await sendNebulaMessageStream(
          message,
          contextFilter,
          sessionId,
          (chunk: string) => {
            // Call the onChunk callback with each streaming chunk
            onChunk?.(chunk);
          },
          (metadata: any) => {
            // Handle completion with metadata
            finalResponse = {
              response: "", // The full response was built through chunks
              session_id: metadata.session_id,
              request_id: metadata.request_id,
              actions: metadata.actions,
              transactions: metadata.transactions,
            };
          },
          (error: string) => {
            console.error("Streaming error:", error);
            toast({
              title: "Streaming Error",
              description: error,
              variant: "destructive",
            });
          }
        );

        toast({
          title: "Message sent",
          description: "Received streaming response from Nebula AI",
        });

        return finalResponse;
      } catch (error: any) {
        console.error("Error sending streaming message to Nebula:", error);

        toast({
          title: "Error sending message",
          description:
            error.message || "Failed to send streaming message to Nebula",
          variant: "destructive",
        });

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [sessionId, account?.address, activeChain?.id]
  );

  return {
    sendMessage,
    sendStreamingMessage,
    isLoading,
    sessionId,
  };
}
