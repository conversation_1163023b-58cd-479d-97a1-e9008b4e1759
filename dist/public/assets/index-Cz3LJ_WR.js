const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/signing-CVo8kY2Q.js","assets/index-BLCiG1f1.js","assets/index-DcbjDVZw.css","assets/concat-hex-DFAKUy4s.js","assets/AbiFunction-DqE8RxZT.js","assets/Signature-DGO4cPHi.js","assets/approve-DWFDVmM0.js","assets/send-eip712-transaction-v3D52bEy.js","assets/eth_sendRawTransaction-DPdnXbFR.js","assets/sha256-tFU1VRl1.js"])))=>i.map(i=>d[i]);
import{r as D,p as I,o as dt,Z as K,t as z,a as mt,i as j,w as pt,b as W,s as Z,c as S,d as x,e as L,g as Y,h as ut,f as J,j as C,E as O,k as yt,l as lt,m as k,n as R,q as b,u as ft,v as At,x as wt,D as Q,y as ht,z as gt,A as Gt,B as E,C as Tt,F as v,G as X,H as Pt,I as vt,J as xt,K as Lt,L as Ot,M as tt,N as F,O as V,_ as M,P as Dt,Q as Ct,R as St,S as Ft,T as bt,U as Et}from"./index-BLCiG1f1.js";import{a as Vt,b as Ut}from"./approve-DWFDVmM0.js";import{t as It,p as _t,s as $t}from"./send-eip712-transaction-v3D52bEy.js";const Nt="0xf15d424e",kt=[{type:"address",name:"signer"}],Rt=[{type:"tuple",name:"permissions",components:[{type:"address",name:"signer"},{type:"address[]",name:"approvedTargets"},{type:"uint256",name:"nativeTokenLimitPerTransaction"},{type:"uint128",name:"startTimestamp"},{type:"uint128",name:"endTimestamp"}]}];async function Ht(e){return D({contract:e.contract,method:[Nt,kt,Rt],params:[e.signer]})}const Bt="0x5892e236",Mt=[{type:"tuple",name:"req",components:[{type:"address",name:"signer"},{type:"uint8",name:"isAdmin"},{type:"address[]",name:"approvedTargets"},{type:"uint256",name:"nativeTokenLimitPerTransaction"},{type:"uint128",name:"permissionStartTimestamp"},{type:"uint128",name:"permissionEndTimestamp"},{type:"uint128",name:"reqValidityStartTimestamp"},{type:"uint128",name:"reqValidityEndTimestamp"},{type:"bytes32",name:"uid"}]},{type:"bytes",name:"signature"}],qt=[];function Kt(e){const n=dt(async()=>"asyncParams"in e?await e.asyncParams():e);return I({contract:e.contract,method:[Bt,Mt,qt],params:async()=>{const t=await n();return[t.req,t.signature]},value:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.value},accessList:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.accessList},gas:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.gas},gasPrice:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.gasPrice},maxFeePerGas:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.maxFeePerGas},maxPriorityFeePerGas:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.maxPriorityFeePerGas},nonce:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.nonce},extraGas:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.extraGas},erc20Value:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.erc20Value},authorizationList:async()=>{var t;return(t=(await n()).overrides)==null?void 0:t.authorizationList}})}function q(){return new Date(Date.now()+1e3*60*60*24*365*10)}function _(e){return It(Math.floor(e.getTime()/1e3))}const zt=[{name:"signer",type:"address"},{name:"isAdmin",type:"uint8"},{name:"approvedTargets",type:"address[]"},{name:"nativeTokenLimitPerTransaction",type:"uint256"},{name:"permissionStartTimestamp",type:"uint128"},{name:"permissionEndTimestamp",type:"uint128"},{name:"reqValidityStartTimestamp",type:"uint128"},{name:"reqValidityEndTimestamp",type:"uint128"},{name:"uid",type:"bytes32"}];async function jt(e){const{account:n,contract:t,req:r}=e,i=await n.signTypedData({domain:{name:"Account",version:"1",verifyingContract:t.address,chainId:t.chain.id},primaryType:"SignerPermissionRequest",types:{SignerPermissionRequest:zt},message:r});return{req:r,signature:i}}async function Wt(e){var r;const{target:n,permissions:t}=e;return{approvedTargets:t.approvedTargets==="*"?[K]:t.approvedTargets,nativeTokenLimitPerTransaction:z(((r=t.nativeTokenLimitPerTransaction)==null?void 0:r.toString())||"0"),permissionStartTimestamp:_(t.permissionStartTimestamp||new Date(0)),permissionEndTimestamp:_(t.permissionEndTimestamp||q()),reqValidityStartTimestamp:0n,reqValidityEndTimestamp:_(q()),uid:await mt(),isAdmin:0,signer:n}}function Zt(e){const{contract:n,sessionKeyAddress:t,account:r,permissions:i}=e;return Kt({contract:n,async asyncParams(){const{req:a,signature:c}=await jt({account:r,contract:n,req:await Wt({target:t,permissions:i})});return{signature:c,req:a}}})}async function Yt(e){var c;const{accountContract:n,sessionKeyAddress:t,newPermissions:r}=e;if(!await j(n))return!0;const a=await Ht({contract:n,signer:t});return!!(a.endTimestamp&&a.endTimestamp<Math.floor(new Date().getTime()/1e3)||!Jt(a.approvedTargets,r.approvedTargets)||z(((c=r.nativeTokenLimitPerTransaction)==null?void 0:c.toString())??"0")>a.nativeTokenLimitPerTransaction)}function Jt(e,n){return n==="*"&&e.length===1&&e[0]===K?!0:n!=="*"?n.map(t=>t.toLowerCase()).every(t=>e.map(r=>r.toLowerCase()).includes(t)):!1}const Qt=2n**96n-1n;async function Xt(e){const{factoryContract:n,predictAddressOverride:t,adminAddress:r,accountSalt:i,accountAddress:a}=e;if(t)return t(n,r);if(a)return a;if(!r)throw new Error("Account address is required to predict the smart wallet address.");return pt(async()=>{const c=i&&W(i)?i:Z(i??"");let s,o=0;const p=3;for(;o<=p;)try{s=await D({contract:n,method:"function getAddress(address, bytes) returns (address)",params:[r,c]});break}catch(y){if(o===p)throw y;const l=2**(o+1)*200;await new Promise(d=>setTimeout(d,l)),o++}if(!s)throw new Error(`No smart account address found for admin address ${r} and salt ${i}`);return s},{cacheKey:`${e.factoryContract.chain.id}-${e.factoryContract.address}-${e.adminAddress}-${e.accountSalt}`,cacheTime:1e3*60*60*24})}function et(e){const{adminAddress:n,factoryContract:t,createAccountOverride:r,accountSalt:i}=e;if(r)return r(t,n);const a=i&&W(i)?i:Z(i??"");return I({contract:t,method:"function createAccount(address, bytes) returns (address)",params:[n,a]})}function at(e){const{accountContract:n,transaction:t,executeOverride:r}=e;if(r)return r(n,t);let i=t.value||0n;return(t.chainId===295||t.chainId===296)&&(i=BigInt(i)/BigInt(10**10)),I({contract:n,method:"function execute(address, uint256, bytes)",params:[t.to||"",i,t.data||"0x"],gas:t.gas?t.gas+21000n:void 0})}function te(e){var c;const{accountContract:n,transactions:t,executeBatchOverride:r}=e;if(r)return r(n,t);let i=t.map(s=>s.value||0n);const a=(c=t[0])==null?void 0:c.chainId;return(a===295||a===296)&&(i=i.map(s=>BigInt(s)/BigInt(10**10))),I({contract:n,method:"function executeBatch(address[], uint256[], bytes[])",params:[t.map(s=>s.to||""),i,t.map(s=>s.data||"0x")]})}const ee="0x35567e1a",ae=[{type:"address",name:"sender"},{type:"uint192",name:"key"}],ne=[{type:"uint256",name:"nonce"}];async function se(e){return D({contract:e.contract,method:[ee,ae,ne],params:[e.sender,e.key]})}const re="0xa6193531",ie=[{type:"tuple",name:"userOp",components:[{type:"address",name:"sender"},{type:"uint256",name:"nonce"},{type:"bytes",name:"initCode"},{type:"bytes",name:"callData"},{type:"uint256",name:"callGasLimit"},{type:"uint256",name:"verificationGasLimit"},{type:"uint256",name:"preVerificationGas"},{type:"uint256",name:"maxFeePerGas"},{type:"uint256",name:"maxPriorityFeePerGas"},{type:"bytes",name:"paymasterAndData"},{type:"bytes",name:"signature"}]}],ce=[{type:"bytes32"}];async function oe(e){return D({contract:e.contract,method:[re,ie,ce],params:[e.userOp]})}const de="0x22cdde4c",me=[{type:"tuple",name:"userOp",components:[{type:"address",name:"sender"},{type:"uint256",name:"nonce"},{type:"bytes",name:"initCode"},{type:"bytes",name:"callData"},{type:"bytes32",name:"accountGasLimits"},{type:"uint256",name:"preVerificationGas"},{type:"bytes32",name:"gasFees"},{type:"bytes",name:"paymasterAndData"},{type:"bytes",name:"signature"}]}],pe=[{type:"bytes32"}];async function ue(e){return D({contract:e.contract,method:[de,me,pe],params:[e.userOp]})}function ye(e){return e.factory?S([e.factory,e.factoryData||"0x"]):"0x"}function le(e){return S([x(L(BigInt(e.verificationGasLimit)),{size:16}),x(L(BigInt(e.callGasLimit)),{size:16})])}function fe(e){return S([x(L(BigInt(e.maxPriorityFeePerGas)),{size:16}),x(L(BigInt(e.maxFeePerGas)),{size:16})])}function Ae(e){return e.paymaster?S([e.paymaster,x(L(BigInt(e.paymasterVerificationGasLimit||0)),{size:16}),x(L(BigInt(e.paymasterPostOpGasLimit||0)),{size:16}),e.paymasterData||"0x"]):"0x"}const we=e=>({sender:e.sender,nonce:BigInt(e.nonce),initCode:ye(e),callData:e.callData,accountGasLimits:le(e),preVerificationGas:BigInt(e.preVerificationGas),gasFees:fe(e),paymasterAndData:Ae(e),signature:e.signature});async function U(e){var h;const{userOp:n,paymasterOverride:t,client:r,chain:i,entrypointAddress:a}=e;if(t)return t(n);const c={"Content-Type":"application/json"},s=a??O,o=Y(i),p={jsonrpc:"2.0",id:1,method:"pm_sponsorUserOperation",params:[ut(n),s]},l=await yt(r)(o,{method:"POST",headers:c,body:J(p)});if(!l.ok){const g=await l.text()||l.statusText;throw new Error(`Paymaster error: ${l.status} - ${g}`)}const d=await l.json();if(d.result)return typeof d.result=="string"?{paymasterAndData:d.result}:(d.result.reason&&console.warn(`Paymaster policy rejected this transaction with reason: ${d.result.reason} ${d.result.policyId?`(policyId: ${d.result.policyId})`:""}`),{paymasterAndData:d.result.paymasterAndData,verificationGasLimit:d.result.verificationGasLimit?C(d.result.verificationGasLimit):void 0,preVerificationGas:d.result.preVerificationGas?C(d.result.preVerificationGas):void 0,callGasLimit:d.result.callGasLimit?C(d.result.callGasLimit):void 0,paymaster:d.result.paymaster,paymasterData:d.result.paymasterData,paymasterVerificationGasLimit:d.result.paymasterVerificationGasLimit?C(d.result.paymasterVerificationGasLimit):void 0,paymasterPostOpGasLimit:d.result.paymasterPostOpGasLimit?C(d.result.paymasterPostOpGasLimit):void 0});const w=((h=d.error)==null?void 0:h.message)||d.error||l.statusText||"unknown error";throw new Error(`Paymaster error from ${o}: ${w}`)}const H=new Set,B=e=>`${e.chain.id}:${e.address}`,nt=e=>{H.add(B(e))},st=e=>{H.delete(B(e))},rt=e=>H.has(B(e));async function he(e){const n=e.timeoutMs||12e4,t=e.intervalMs||1e3,r=Date.now()+n;for(;Date.now()<r;){const i=await lt(e);if(i)return i;await new Promise(a=>setTimeout(a,t))}throw new Error(`Timeout waiting for userOp to be mined on chain ${e.chain.id} with UserOp hash: ${e.userOpHash}`)}async function ge(e){var A;const{transaction:n,accountContract:t,factoryContract:r,adminAddress:i,overrides:a,sponsorGas:c,waitForDeployment:s=!0,isDeployedOverride:o}=e,p=n.chain,y=n.client,l={client:y,chain:p,bundlerUrl:a==null?void 0:a.bundlerUrl,entrypointAddress:a==null?void 0:a.entrypointAddress},d=k(((A=e.overrides)==null?void 0:A.entrypointAddress)||O),[w,h,g,G,f]=await Promise.all([typeof o=="boolean"?o:j(t).then(T=>T||rt(t)),R(n),b(n.gas),Ge({executeTx:n,bundlerOptions:l,chain:p,client:y}),Oe({accountContract:t,chain:p,client:y,entrypointAddress:a==null?void 0:a.entrypointAddress,getNonceOverride:a==null?void 0:a.getAccountNonce})]),{maxFeePerGas:m,maxPriorityFeePerGas:u}=G;return d==="v0.7"?Te({bundlerOptions:l,factoryContract:r,accountContract:t,adminAddress:i,sponsorGas:c,overrides:a,isDeployed:w,nonce:f,callData:h,callGasLimit:g,maxFeePerGas:m,maxPriorityFeePerGas:u,waitForDeployment:s}):Pe({bundlerOptions:l,factoryContract:r,accountContract:t,adminAddress:i,sponsorGas:c,overrides:a,isDeployed:w,nonce:f,callData:h,callGasLimit:g,maxFeePerGas:m,maxPriorityFeePerGas:u,waitForDeployment:s})}async function Ge(e){const{executeTx:n,bundlerOptions:t,chain:r,client:i}=e;let{maxFeePerGas:a,maxPriorityFeePerGas:c}=n;const s=(t==null?void 0:t.bundlerUrl)??Y(r);if(ft(s)){const o=await At({options:t});a=o.maxFeePerGas,c=o.maxPriorityFeePerGas}else{const[o,p]=await Promise.all([b(a),b(c)]);if(o&&p)a=o,c=p;else{const y=await wt(i,r);c=p??y.maxPriorityFeePerGas??0n,a=o??y.maxFeePerGas??0n}}return{maxFeePerGas:a,maxPriorityFeePerGas:c}}async function Te(e){const{bundlerOptions:n,isDeployed:t,factoryContract:r,accountContract:i,adminAddress:a,sponsorGas:c,overrides:s,nonce:o,callData:p,callGasLimit:y,maxFeePerGas:l,maxPriorityFeePerGas:d,waitForDeployment:w}=e,{chain:h,client:g}=n;let G,f;t?(f="0x",w&&await it(i)):(G=r.address,f=await R(et({factoryContract:r,adminAddress:a,accountSalt:s==null?void 0:s.accountSalt,createAccountOverride:s==null?void 0:s.createAccount})),w&&nt(i));const m={sender:i.address,nonce:o,callData:p,maxFeePerGas:l,maxPriorityFeePerGas:d,callGasLimit:y??0n,verificationGasLimit:0n,preVerificationGas:0n,factory:G,factoryData:f,paymaster:void 0,paymasterData:"0x",paymasterVerificationGasLimit:0n,paymasterPostOpGasLimit:0n,signature:Q};if(c){const u=await U({userOp:m,chain:h,client:g,entrypointAddress:s==null?void 0:s.entrypointAddress,paymasterOverride:s==null?void 0:s.paymaster});if(u.paymaster&&u.paymasterData&&(m.paymaster=u.paymaster,m.paymasterData=u.paymasterData),u.callGasLimit&&u.verificationGasLimit&&u.preVerificationGas&&u.paymasterPostOpGasLimit&&u.paymasterVerificationGasLimit)m.callGasLimit=u.callGasLimit,m.verificationGasLimit=u.verificationGasLimit,m.preVerificationGas=u.preVerificationGas,m.paymasterPostOpGasLimit=u.paymasterPostOpGasLimit,m.paymasterVerificationGasLimit=u.paymasterVerificationGasLimit;else{const A=s!=null&&s.tokenPaymaster?{[s.tokenPaymaster.tokenAddress]:{stateDiff:{[ht(gt([{type:"address"},{type:"uint256"}],[i.address,s.tokenPaymaster.balanceStorageSlot]))]:Gt(vt,{size:32})}}}:void 0,T=await E({userOp:m,options:n},A);m.callGasLimit=T.callGasLimit,m.verificationGasLimit=T.verificationGasLimit,m.preVerificationGas=T.preVerificationGas,m.paymasterPostOpGasLimit=s!=null&&s.tokenPaymaster?500000n:T.paymasterPostOpGasLimit||0n,m.paymasterVerificationGasLimit=T.paymasterVerificationGasLimit||0n;const P=await U({userOp:m,chain:h,client:g,entrypointAddress:s==null?void 0:s.entrypointAddress,paymasterOverride:s==null?void 0:s.paymaster});P.paymaster&&P.paymasterData&&(m.paymaster=P.paymaster,m.paymasterData=P.paymasterData)}}else{const u=await E({userOp:m,options:n});m.callGasLimit=u.callGasLimit,m.verificationGasLimit=u.verificationGasLimit,m.preVerificationGas=u.preVerificationGas,m.paymasterPostOpGasLimit=u.paymasterPostOpGasLimit||0n,m.paymasterVerificationGasLimit=u.paymasterVerificationGasLimit||0n}return{...m,signature:"0x"}}async function Pe(e){const{bundlerOptions:n,isDeployed:t,factoryContract:r,accountContract:i,adminAddress:a,sponsorGas:c,overrides:s,nonce:o,callData:p,callGasLimit:y,maxFeePerGas:l,maxPriorityFeePerGas:d,waitForDeployment:w}=e,{chain:h,client:g}=n;let G;t?(G="0x",w&&await it(i)):(G=await Le({factoryContract:r,adminAddress:a,accountSalt:s==null?void 0:s.accountSalt,createAccountOverride:s==null?void 0:s.createAccount}),w&&nt(i));const f={sender:i.address,nonce:o,initCode:G,callData:p,maxFeePerGas:l,maxPriorityFeePerGas:d,callGasLimit:y??0n,verificationGasLimit:0n,preVerificationGas:0n,paymasterAndData:"0x",signature:Q};if(c){const m=await U({userOp:f,chain:h,client:g,entrypointAddress:s==null?void 0:s.entrypointAddress,paymasterOverride:s==null?void 0:s.paymaster}),u="paymasterAndData"in m?m.paymasterAndData:"0x";if(u&&u!=="0x"&&(f.paymasterAndData=u),m.callGasLimit&&m.verificationGasLimit&&m.preVerificationGas)f.callGasLimit=m.callGasLimit,f.verificationGasLimit=m.verificationGasLimit,f.preVerificationGas=m.preVerificationGas;else{const A=await E({userOp:f,options:n});if(f.callGasLimit=A.callGasLimit,f.verificationGasLimit=A.verificationGasLimit,f.preVerificationGas=A.preVerificationGas,u&&u!=="0x"){const T=await U({userOp:f,chain:h,client:g,entrypointAddress:s==null?void 0:s.entrypointAddress,paymasterOverride:s==null?void 0:s.paymaster}),P="paymasterAndData"in T?T.paymasterAndData:"0x";P&&P!=="0x"&&(f.paymasterAndData=P)}}}else{const m=await E({userOp:f,options:n});f.callGasLimit=m.callGasLimit,f.verificationGasLimit=m.verificationGasLimit,f.preVerificationGas=m.preVerificationGas}return{...f,signature:"0x"}}async function ve(e){const{userOp:n,chain:t,entrypointAddress:r,adminAccount:i}=e,a=await xe({client:e.client,userOp:n,chain:t,entrypointAddress:r});if(i.signMessage){const c=await i.signMessage({message:{raw:Tt(a)},originalMessage:J(n),chainId:t.id});return{...n,signature:c}}throw new Error("signMessage not implemented in signingAccount")}async function xe(e){const{userOp:n,chain:t,entrypointAddress:r}=e,i=k(r||O);let a;if(i==="v0.7"){const c=we(n);a=await ue({contract:v({address:r||X,chain:t,client:e.client}),userOp:c})}else a=await oe({contract:v({address:r||O,chain:t,client:e.client}),userOp:n});return a}async function Le(e){const{factoryContract:n,adminAddress:t,accountSalt:r,createAccountOverride:i}=e,a=et({factoryContract:n,adminAddress:t,accountSalt:r,createAccountOverride:i});return S([n.address,await R(a)])}async function Oe(e){const{accountContract:n,chain:t,client:r,entrypointAddress:i,getNonceOverride:a}=e;return a?a(n):await se({contract:v({address:i||O,chain:t,client:r}),key:Pt(),sender:n.address})}async function it(e){const n=Date.now();for(;rt(e);){if(Date.now()-n>6e4)throw st(e),new Error("Account deployment is taking too long (over 1 minute). Please try again.");await new Promise(t=>setTimeout(t,500))}}const ct=new WeakMap,$=new WeakMap;async function De(e,n){var w,h,g,G,f,m,u;const{personalAccount:t,client:r,chain:i}=e;if(!t)throw new Error("No personal account provided for smart account connection");const a=n,c=i??a.chain,s="gasless"in a?a.gasless:a.sponsorGas;if(await xt(c))return[be({creationOptions:n,connectionOptions:e,chain:c,sponsorGas:s}),c];if(a.factoryAddress&&!((w=a.overrides)!=null&&w.entrypointAddress)){const A=await ot(a.factoryAddress,r,c);A&&(a.overrides={...a.overrides,entrypointAddress:A})}(h=a.overrides)!=null&&h.tokenPaymaster&&!((g=a.overrides)!=null&&g.entrypointAddress)&&(a.overrides={...a.overrides,entrypointAddress:X});const o=a.factoryAddress??Lt((G=a.overrides)==null?void 0:G.entrypointAddress),p=v({client:r,address:o,chain:c}),y=await Xt({factoryContract:p,adminAddress:t.address,predictAddressOverride:(f=a.overrides)==null?void 0:f.predictAddress,accountSalt:(m=a.overrides)==null?void 0:m.accountSalt,accountAddress:(u=a.overrides)==null?void 0:u.accountAddress}).then(A=>A).catch(A=>{throw new Error(`Failed to get account address with factory contract ${p.address} on chain ID ${c.id}: ${(A==null?void 0:A.message)||"unknown error"}`,{cause:A})}),l=v({client:r,address:y,chain:c}),d=await Se({...a,chain:c,sponsorGas:s,personalAccount:t,accountContract:l,factoryContract:p,client:r});if(ct.set(t,d),$.set(d,t),a.sessionKey&&await Yt({accountContract:l,sessionKeyAddress:a.sessionKey.address,newPermissions:a.sessionKey.permissions})){const A=Zt({account:t,contract:l,permissions:a.sessionKey.permissions,sessionKeyAddress:a.sessionKey.address});await Ot({account:d,transaction:A})}return[d,c]}async function Ce(e){const n=$.get(e);n&&(ct.delete(n),$.delete(e))}async function Se(e){var i,a;const n=(i=e.overrides)==null?void 0:i.tokenPaymaster;if(n&&k(((a=e.overrides)==null?void 0:a.entrypointAddress)||O)!=="v0.7")throw new Error("Token paymaster is only supported for entrypoint version v0.7");let t=e.accountContract;const r={address:tt(t.address),async sendTransaction(c){var l,d,w;let s;if(n){await Fe({accountContract:t,erc20Paymaster:n,options:e});const h=async()=>({paymaster:n.paymasterAddress,paymasterData:"0x"});s=((l=e.overrides)==null?void 0:l.paymaster)||h}else s=(d=e.overrides)==null?void 0:d.paymaster;c.chainId!==t.chain.id&&(t=v({address:r.address,chain:F(c.chainId),client:e.client}));const o=at({accountContract:t,transaction:c,executeOverride:(w=e.overrides)==null?void 0:w.execute}),p=F(c.chainId),y=await N({executeTx:o,options:{...e,chain:p,accountContract:t,overrides:{...e.overrides,paymaster:s}}});return V({client:e.client,chainId:p.id,transactionHash:y.transactionHash,walletAddress:e.accountContract.address,walletType:"smart",contractAddress:c.to??void 0}),y},async sendBatchTransaction(c){var l,d;const s=te({accountContract:t,transactions:c,executeBatchOverride:(l=e.overrides)==null?void 0:l.executeBatch});if(c.length===0)throw new Error("No transactions to send");const o=c[0];if(!o)throw new Error("No transactions to send");const p=F(o.chainId),y=await N({executeTx:s,options:{...e,chain:p,accountContract:t}});return V({client:e.client,chainId:p.id,transactionHash:y.transactionHash,walletAddress:e.accountContract.address,walletType:"smart",contractAddress:((d=c[0])==null?void 0:d.to)??void 0}),y},async signMessage({message:c}){var o;if((o=e.overrides)!=null&&o.signMessage)return e.overrides.signMessage({adminAccount:e.personalAccount,factoryContract:e.factoryContract,accountContract:t,message:c});const{smartAccountSignMessage:s}=await M(async()=>{const{smartAccountSignMessage:p}=await import("./signing-CVo8kY2Q.js");return{smartAccountSignMessage:p}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]));return s({accountContract:t,factoryContract:e.factoryContract,options:e,message:c})},async signTypedData(c){var o;if((o=e.overrides)!=null&&o.signTypedData)return e.overrides.signTypedData({adminAccount:e.personalAccount,factoryContract:e.factoryContract,accountContract:t,typedData:c});const{smartAccountSignTypedData:s}=await M(async()=>{const{smartAccountSignTypedData:p}=await import("./signing-CVo8kY2Q.js");return{smartAccountSignTypedData:p}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]));return s({accountContract:t,factoryContract:e.factoryContract,options:e,typedData:c})},async onTransactionRequested(c){var s,o;return(o=(s=e.personalAccount).onTransactionRequested)==null?void 0:o.call(s,c)}};return r}async function Fe(e){var y;const{accountContract:n,erc20Paymaster:t,options:r}=e,i=t.tokenAddress,a=v({address:i,chain:n.chain,client:n.client});if(await Vt({contract:a,owner:n.address,spender:t.paymasterAddress})>0n)return;const s=Ut({contract:a,spender:t.paymasterAddress,amountWei:Qt-1n}),o=await Dt({transaction:s,from:n.address}),p=at({accountContract:n,transaction:o,executeOverride:(y=r.overrides)==null?void 0:y.execute});await N({executeTx:p,options:{...r,overrides:{...r.overrides,tokenPaymaster:void 0}}})}function be(e){const{creationOptions:n,connectionOptions:t,chain:r}=e,i={address:tt(t.personalAccount.address),async sendTransaction(a){var y,l,d,w;const c={data:a.data,to:a.to??void 0,value:a.value??0n,chain:F(a.chainId),client:t.client,eip712:a.eip712};let s=await _t({account:i,transaction:c});if(e.sponsorGas&&!s.paymaster){const h=await Ct({options:{client:t.client,chain:r,bundlerUrl:(y=n.overrides)==null?void 0:y.bundlerUrl,entrypointAddress:(l=n.overrides)==null?void 0:l.entrypointAddress},transaction:s});s={...s,...h}}const o=await $t({account:i,chainId:r.id,eip712Transaction:s}),p=await St({options:{client:t.client,chain:r,bundlerUrl:(d=n.overrides)==null?void 0:d.bundlerUrl,entrypointAddress:(w=n.overrides)==null?void 0:w.entrypointAddress},transaction:s,signedTransaction:o});return V({client:t.client,chainId:r.id,transactionHash:p.transactionHash,walletAddress:i.address,walletType:"smart",contractAddress:a.to??void 0}),{transactionHash:p.transactionHash,client:t.client,chain:r}},async signMessage({message:a}){return t.personalAccount.signMessage({message:a})},async signTypedData(a){const c=Ft(a);return t.personalAccount.signTypedData(c)},async onTransactionRequested(a){var c,s;return(s=(c=t.personalAccount).onTransactionRequested)==null?void 0:s.call(c,a)}};return i}async function N(e){var r,i,a;const{executeTx:n,options:t}=e;try{const c=await ge({transaction:n,factoryContract:t.factoryContract,accountContract:t.accountContract,adminAddress:t.personalAccount.address,sponsorGas:t.sponsorGas,overrides:t.overrides}),s=await ve({client:t.client,chain:t.chain,adminAccount:t.personalAccount,entrypointAddress:(r=t.overrides)==null?void 0:r.entrypointAddress,userOp:c}),o={chain:t.chain,client:t.client,bundlerUrl:(i=t.overrides)==null?void 0:i.bundlerUrl,entrypointAddress:(a=t.overrides)==null?void 0:a.entrypointAddress},p=await bt({options:o,userOp:s}),y=await he({...o,userOpHash:p});return V({client:t.client,chainId:t.chain.id,transactionHash:y.transactionHash,walletAddress:t.accountContract.address,walletType:"smart",contractAddress:await b(n.to??void 0)}),{client:t.client,chain:t.chain,transactionHash:y.transactionHash}}finally{st(t.accountContract)}}async function ot(e,n,t){const r=v({address:e,client:n,chain:t});try{return await D({contract:r,method:"function entrypoint() public view returns (address)"})}catch{return}}const Ie=Object.freeze(Object.defineProperty({__proto__:null,connectSmartAccount:De,disconnectSmartAccount:Ce,getEntrypointFromFactory:ot,isSmartWallet:Et},Symbol.toStringTag,{value:"Module"}));export{Ie as i,et as p};
