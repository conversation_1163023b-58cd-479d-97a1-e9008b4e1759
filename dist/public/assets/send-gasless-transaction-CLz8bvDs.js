const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/biconomy-BRDWrIf2.js","assets/index-Cx0gGTCY.js","assets/index-BQIEuba7.css","assets/openzeppelin-1KaMAbEf.js","assets/engine-BTMl4e-9.js"])))=>i.map(i=>d[i]);
import{_ as p,R as d}from"./index-Cx0gGTCY.js";async function s({account:e,transaction:i,serializableTransaction:a,gasless:n}){if(a.value&&a.value>0n)throw new Error("Gasless transactions cannot have a value");let r;if(n.provider==="biconomy"){const{relayBiconomyTransaction:o}=await p(async()=>{const{relayBiconomyTransaction:t}=await import("./biconomy-BRDWrIf2.js");return{relayBiconomyTransaction:t}},__vite__mapDeps([0,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="openzeppelin"){const{relayOpenZeppelinTransaction:o}=await p(async()=>{const{relayOpenZeppelinTransaction:t}=await import("./openzeppelin-1KaMAbEf.js");return{relayOpenZeppelinTransaction:t}},__vite__mapDeps([3,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(n.provider==="engine"){const{relayEngineTransaction:o}=await p(async()=>{const{relayEngineTransaction:t}=await import("./engine-BTMl4e-9.js");return{relayEngineTransaction:t}},__vite__mapDeps([4,1,2]));r=await o({account:e,transaction:i,serializableTransaction:a,gasless:n})}if(!r)throw new Error("Unsupported gasless provider");return d({address:e.address,transactionHash:r.transactionHash,chainId:i.chain.id}),r}export{s as sendGaslessTransaction};
