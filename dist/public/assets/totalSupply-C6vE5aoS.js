import{z as r,A as a,w as c}from"./index-Cx0gGTCY.js";import{d as u}from"./detectExtension-CPXZ7KZa.js";const e="0xbd85b039",o=[{type:"uint256",name:"id"}],n=[{type:"uint256"}];function s(t){return u({availableSelectors:t,method:[e,o,n]})}function d(t){return r(o,[t.id])}function l(t){return e+d(t).slice(2)}function m(t){return a(n,t)[0]}async function S(t){return c({contract:t.contract,method:[e,o,n],params:[t.id]})}export{e as FN_SELECTOR,m as decodeTotalSupplyResult,l as encodeTotalSupply,d as encodeTotalSupplyParams,s as isTotalSupplySupported,S as totalSupply};
