const t=e=>({connectionScreen:{inProgress:"확인 대기 중",failed:"연결 실패",instruction:`${e}에서 연결 요청을 수락하세요`,retry:"다시 시도하세요"},getStartedScreen:{instruction:`Scan the QR code to download the ${e} app`},scanScreen:{instruction:`Scan the QR code with the ${e} app to connect`},getStartedLink:`Don't have ${e}?`,download:{chrome:"Chrome 확장 프로그램 다운로드",android:"Google Play에서 다운로드",iOS:"App Store에서 다운로드"}});export{t as default};
